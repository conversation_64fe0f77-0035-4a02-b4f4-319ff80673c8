#!/usr/bin/env node

const { exec, spawn } = require('child_process');
const util = require('util');
const path = require('path');
const fs = require('fs');

const execAsync = util.promisify(exec);

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

// Services to build
const services = [
  'auth-user-service',
  'course-learning-service',
  'payment-subscription-service',
  'reporting-analytics-service'
];

// Check if Docker is running
async function checkDockerRunning() {
  try {
    await execAsync('docker info');
    return true;
  } catch (error) {
    return false;
  }
}

// Clean up existing containers and volumes
async function cleanup() {
  logInfo('Cleaning up existing containers and volumes...');
  
  try {
    // Stop and remove containers
    await execAsync('docker-compose -f docker-compose.dev.yml down -v --remove-orphans');
    logSuccess('Cleaned up existing containers and volumes');
  } catch (error) {
    logWarning('Cleanup warning: ' + error.message);
  }
}

// Install dependencies for all services
async function installDependencies() {
  logInfo('Installing dependencies for all services...');
  
  try {
    await execAsync('npm run install:all');
    logSuccess('Dependencies installed successfully');
  } catch (error) {
    logError('Failed to install dependencies: ' + error.message);
    throw error;
  }
}

// Build all services
async function buildServices() {
  logInfo('Building all services...');
  
  for (const service of services) {
    try {
      logInfo(`Building ${service}...`);
      const servicePath = path.join('services', service);
      
      // Check if service directory exists
      if (!fs.existsSync(servicePath)) {
        logError(`Service directory not found: ${servicePath}`);
        continue;
      }
      
      // Build the service
      await execAsync(`cd ${servicePath} && npm run build`);
      logSuccess(`${service} built successfully`);
    } catch (error) {
      logError(`Failed to build ${service}: ${error.message}`);
      throw error;
    }
  }
}

// Start services with Docker Compose
async function startServices() {
  logInfo('Starting services with Docker Compose...');
  
  return new Promise((resolve, reject) => {
    const dockerCompose = spawn('docker-compose', [
      '-f', 'docker-compose.dev.yml',
      'up', '--build', '--remove-orphans'
    ], {
      stdio: 'inherit'
    });
    
    dockerCompose.on('error', (error) => {
      logError('Failed to start Docker Compose: ' + error.message);
      reject(error);
    });
    
    // Don't wait for docker-compose to finish, as it runs indefinitely
    setTimeout(() => {
      logSuccess('Docker Compose started successfully');
      resolve();
    }, 5000);
  });
}

// Wait for services to be ready
async function waitForServices() {
  logInfo('Waiting for services to be ready...');
  
  const maxAttempts = 30;
  const delay = 10000; // 10 seconds
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      logInfo(`Health check attempt ${attempt}/${maxAttempts}...`);
      
      // Import and run the test script
      const { runTests } = require('./test-services.js');
      const allHealthy = await runTests();
      
      if (allHealthy) {
        logSuccess('All services are ready and healthy!');
        return true;
      }
      
      if (attempt < maxAttempts) {
        logInfo(`Waiting ${delay/1000} seconds before next attempt...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    } catch (error) {
      logWarning(`Health check attempt ${attempt} failed: ${error.message}`);
      
      if (attempt < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  logError('Services did not become healthy within the expected time');
  return false;
}

// Show service URLs
function showServiceUrls() {
  log(`${colors.bold}🌐 Service URLs${colors.reset}`);
  log('API Gateway: http://localhost:8080', colors.blue);
  log('Auth Service: http://localhost:3001', colors.blue);
  log('Course Service: http://localhost:3002', colors.blue);
  log('Payment Service: http://localhost:3003', colors.blue);
  log('Analytics Service: http://localhost:3004', colors.blue);
  log('RabbitMQ Management: http://localhost:15672 (admin/password)', colors.blue);
  log('MongoDB Auth: mongodb://localhost:27017/timecourse_auth', colors.blue);
  log('MongoDB Courses: mongodb://localhost:27018/timecourse_courses', colors.blue);
  log('MongoDB Payments: mongodb://localhost:27019/timecourse_payments', colors.blue);
  log('MongoDB Analytics: mongodb://localhost:27020/timecourse_analytics', colors.blue);
}

// Main build and deploy function
async function buildAndDeploy() {
  try {
    log(`${colors.bold}🚀 Starting Build and Deployment Process${colors.reset}\n`);
    
    // Check if Docker is running
    const dockerRunning = await checkDockerRunning();
    if (!dockerRunning) {
      logError('Docker is not running. Please start Docker and try again.');
      process.exit(1);
    }
    logSuccess('Docker is running');
    
    // Cleanup existing containers
    await cleanup();
    
    // Install dependencies
    await installDependencies();
    
    // Build services
    await buildServices();
    
    // Start services
    await startServices();
    
    // Wait for services to be ready
    const servicesReady = await waitForServices();
    
    if (servicesReady) {
      log(`${colors.bold}🎉 Deployment Successful!${colors.reset}\n`);
      showServiceUrls();
      
      log(`\n${colors.bold}Next Steps:${colors.reset}`);
      log('1. Test the API endpoints using the provided URLs', colors.blue);
      log('2. Check the logs: docker-compose -f docker-compose.dev.yml logs -f', colors.blue);
      log('3. Stop services: docker-compose -f docker-compose.dev.yml down', colors.blue);
      
    } else {
      logError('Deployment completed but some services are not healthy');
      log('\nTo debug:', colors.yellow);
      log('1. Check logs: docker-compose -f docker-compose.dev.yml logs', colors.yellow);
      log('2. Check container status: docker ps', colors.yellow);
      log('3. Run health checks: node test-services.js', colors.yellow);
      process.exit(1);
    }
    
  } catch (error) {
    logError('Build and deployment failed: ' + error.message);
    process.exit(1);
  }
}

// Handle script arguments
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  log(`${colors.bold}Build and Deploy Script${colors.reset}`);
  log('Usage: node build-and-deploy.js [options]');
  log('');
  log('Options:');
  log('  --help, -h     Show this help message');
  log('  --no-wait      Skip waiting for services to be ready');
  log('  --cleanup-only Only cleanup existing containers');
  log('');
  log('This script will:');
  log('1. Clean up existing containers and volumes');
  log('2. Install dependencies for all services');
  log('3. Build all services');
  log('4. Start services with Docker Compose');
  log('5. Wait for services to be healthy');
  process.exit(0);
}

if (args.includes('--cleanup-only')) {
  cleanup()
    .then(() => {
      logSuccess('Cleanup completed');
      process.exit(0);
    })
    .catch(error => {
      logError('Cleanup failed: ' + error.message);
      process.exit(1);
    });
} else {
  // Run the main build and deploy process
  buildAndDeploy();
}

module.exports = { buildAndDeploy, cleanup, installDependencies, buildServices };
