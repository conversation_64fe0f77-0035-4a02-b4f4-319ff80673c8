#!/usr/bin/env node

const axios = require('axios');
const { exec } = require('child_process');
const util = require('util');

const execAsync = util.promisify(exec);

// Service configuration
const services = [
  {
    name: 'Auth & User Service',
    url: 'http://localhost:3001/health',
    port: 3001
  },
  {
    name: 'Course & Learning Service',
    url: 'http://localhost:3002/health',
    port: 3002
  },
  {
    name: 'Payment & Subscription Service',
    url: 'http://localhost:3003/health',
    port: 3003
  },
  {
    name: 'Reporting & Analytics Service',
    url: 'http://localhost:3004/health',
    port: 3004
  },
  {
    name: 'API Gateway',
    url: 'http://localhost:8080/health',
    port: 8080
  }
];

// Infrastructure services
const infrastructure = [
  {
    name: 'MongoDB Auth',
    port: 27017,
    type: 'mongodb'
  },
  {
    name: 'MongoDB Courses',
    port: 27018,
    type: 'mongodb'
  },
  {
    name: 'MongoDB Payments',
    port: 27019,
    type: 'mongodb'
  },
  {
    name: 'MongoDB Analytics',
    port: 27020,
    type: 'mongodb'
  },
  {
    name: 'RabbitMQ',
    port: 5672,
    managementPort: 15672,
    type: 'rabbitmq'
  }
];

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

// Check if port is open
function checkPort(port, host = 'localhost') {
  return new Promise((resolve) => {
    const net = require('net');
    const socket = new net.Socket();
    
    socket.setTimeout(3000);
    
    socket.on('connect', () => {
      socket.destroy();
      resolve(true);
    });
    
    socket.on('timeout', () => {
      socket.destroy();
      resolve(false);
    });
    
    socket.on('error', () => {
      resolve(false);
    });
    
    socket.connect(port, host);
  });
}

// Test service health endpoint
async function testServiceHealth(service) {
  try {
    logInfo(`Testing ${service.name}...`);
    
    // First check if port is open
    const portOpen = await checkPort(service.port);
    if (!portOpen) {
      logError(`${service.name} - Port ${service.port} is not accessible`);
      return false;
    }
    
    // Test health endpoint
    const response = await axios.get(service.url, {
      timeout: 10000,
      validateStatus: () => true // Accept any status code
    });
    
    if (response.status === 200 && response.data.success) {
      logSuccess(`${service.name} - Healthy`);
      
      // Log additional health information if available
      if (response.data.services) {
        const dbStatus = response.data.services.database?.status || 'unknown';
        const mqStatus = response.data.services.rabbitmq?.status || 'unknown';
        log(`  Database: ${dbStatus}, RabbitMQ: ${mqStatus}`, colors.blue);
        
        if (response.data.uptime) {
          log(`  Uptime: ${response.data.uptime}s`, colors.blue);
        }
      }
      
      return true;
    } else {
      logError(`${service.name} - Unhealthy (Status: ${response.status})`);
      if (response.data.message) {
        log(`  Message: ${response.data.message}`, colors.red);
      }
      return false;
    }
  } catch (error) {
    logError(`${service.name} - Error: ${error.message}`);
    return false;
  }
}

// Test infrastructure services
async function testInfrastructure() {
  logInfo('Testing infrastructure services...');
  
  for (const infra of infrastructure) {
    const portOpen = await checkPort(infra.port);
    
    if (portOpen) {
      logSuccess(`${infra.name} - Port ${infra.port} is accessible`);
      
      // Additional check for RabbitMQ management interface
      if (infra.type === 'rabbitmq' && infra.managementPort) {
        const mgmtPortOpen = await checkPort(infra.managementPort);
        if (mgmtPortOpen) {
          logSuccess(`${infra.name} Management - Port ${infra.managementPort} is accessible`);
        } else {
          logWarning(`${infra.name} Management - Port ${infra.managementPort} is not accessible`);
        }
      }
    } else {
      logError(`${infra.name} - Port ${infra.port} is not accessible`);
    }
  }
}

// Get Docker container status
async function getDockerStatus() {
  try {
    logInfo('Checking Docker container status...');
    const { stdout } = await execAsync('docker ps --format "table {{.Names}}\\t{{.Status}}\\t{{.Ports}}"');
    log(stdout, colors.blue);
  } catch (error) {
    logWarning('Could not get Docker status: ' + error.message);
  }
}

// Main test function
async function runTests() {
  log(`${colors.bold}🚀 Starting Service Health Check Tests${colors.reset}\n`);
  
  // Check Docker status first
  await getDockerStatus();
  console.log();
  
  // Test infrastructure
  await testInfrastructure();
  console.log();
  
  // Test application services
  logInfo('Testing application services...');
  const results = [];
  
  for (const service of services) {
    const result = await testServiceHealth(service);
    results.push({ service: service.name, healthy: result });
    console.log();
  }
  
  // Summary
  log(`${colors.bold}📊 Test Summary${colors.reset}`);
  const healthyCount = results.filter(r => r.healthy).length;
  const totalCount = results.length;
  
  if (healthyCount === totalCount) {
    logSuccess(`All ${totalCount} services are healthy! 🎉`);
  } else {
    logError(`${healthyCount}/${totalCount} services are healthy`);
    
    const unhealthyServices = results.filter(r => !r.healthy);
    log('\nUnhealthy services:', colors.red);
    unhealthyServices.forEach(r => log(`  - ${r.service}`, colors.red));
  }
  
  return healthyCount === totalCount;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      logError('Test execution failed: ' + error.message);
      process.exit(1);
    });
}

module.exports = { runTests, testServiceHealth, checkPort };
