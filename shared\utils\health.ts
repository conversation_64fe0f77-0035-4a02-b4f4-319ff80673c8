import mongoose from 'mongoose';
import { rabbitMQ } from './rabbitmq';

export interface HealthStatus {
  success: boolean;
  message: string;
  timestamp: string;
  services: {
    database: {
      status: 'healthy' | 'unhealthy' | 'unknown';
      message: string;
      responseTime?: number;
    };
    rabbitmq: {
      status: 'healthy' | 'unhealthy' | 'unknown';
      message: string;
      responseTime?: number;
    };
  };
  uptime: number;
  version: string;
}

export class HealthChecker {
  private serviceName: string;
  private version: string;
  private startTime: number;

  constructor(serviceName: string, version: string = '1.0.0') {
    this.serviceName = serviceName;
    this.version = version;
    this.startTime = Date.now();
  }

  async checkDatabase(): Promise<{ status: 'healthy' | 'unhealthy' | 'unknown'; message: string; responseTime?: number }> {
    const startTime = Date.now();
    
    try {
      if (mongoose.connection.readyState === 1) {
        // Connection is open, let's test it with a simple operation
        await mongoose.connection.db.admin().ping();
        const responseTime = Date.now() - startTime;
        
        return {
          status: 'healthy',
          message: `Connected to ${mongoose.connection.host}:${mongoose.connection.port}/${mongoose.connection.name}`,
          responseTime
        };
      } else {
        return {
          status: 'unhealthy',
          message: `Database connection state: ${this.getConnectionStateText(mongoose.connection.readyState)}`
        };
      }
    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      return {
        status: 'unhealthy',
        message: `Database check failed: ${error.message}`,
        responseTime
      };
    }
  }

  async checkRabbitMQ(): Promise<{ status: 'healthy' | 'unhealthy' | 'unknown'; message: string; responseTime?: number }> {
    const startTime = Date.now();
    
    try {
      // Check if RabbitMQ connection and channel are available
      if (rabbitMQ['connection'] && rabbitMQ['channel']) {
        // Try to assert a temporary exchange to test the connection
        const testExchangeName = `health-check-${Date.now()}`;
        await rabbitMQ['channel'].assertExchange(testExchangeName, 'direct', { durable: false, autoDelete: true });
        await rabbitMQ['channel'].deleteExchange(testExchangeName);
        
        const responseTime = Date.now() - startTime;
        return {
          status: 'healthy',
          message: 'RabbitMQ connection is active and responsive',
          responseTime
        };
      } else {
        return {
          status: 'unhealthy',
          message: 'RabbitMQ connection or channel not available'
        };
      }
    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      return {
        status: 'unhealthy',
        message: `RabbitMQ check failed: ${error.message}`,
        responseTime
      };
    }
  }

  async getHealthStatus(): Promise<HealthStatus> {
    const [databaseStatus, rabbitmqStatus] = await Promise.all([
      this.checkDatabase(),
      this.checkRabbitMQ()
    ]);

    const allHealthy = databaseStatus.status === 'healthy' && rabbitmqStatus.status === 'healthy';
    const uptime = Math.floor((Date.now() - this.startTime) / 1000);

    return {
      success: allHealthy,
      message: allHealthy 
        ? `${this.serviceName} is healthy` 
        : `${this.serviceName} has issues with some dependencies`,
      timestamp: new Date().toISOString(),
      services: {
        database: databaseStatus,
        rabbitmq: rabbitmqStatus
      },
      uptime,
      version: this.version
    };
  }

  private getConnectionStateText(state: number): string {
    const states = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting'
    };
    return states[state as keyof typeof states] || 'unknown';
  }
}
