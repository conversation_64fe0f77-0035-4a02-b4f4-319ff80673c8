{"name": "time-course-platform", "version": "1.0.0", "description": "English Online Course Platform with Microservices Architecture", "scripts": {"build": "npm run build:services", "build:services": "npm run build:auth && npm run build:course && npm run build:payment && npm run build:analytics", "build:auth": "cd services/auth-user-service && npm run build", "build:course": "cd services/course-learning-service && npm run build", "build:payment": "cd services/payment-subscription-service && npm run build", "build:analytics": "cd services/reporting-analytics-service && npm run build", "install:all": "npm run install:auth && npm run install:course && npm run install:payment && npm run install:analytics", "install:auth": "cd services/auth-user-service && npm install", "install:course": "cd services/course-learning-service && npm install", "install:payment": "cd services/payment-subscription-service && npm install", "install:analytics": "cd services/reporting-analytics-service && npm install", "dev": "docker-compose -f docker-compose.dev.yml up --build", "prod": "docker-compose -f docker-compose.prod.yml up --build -d", "down": "docker-compose -f docker-compose.dev.yml down", "logs": "docker-compose -f docker-compose.dev.yml logs -f", "deploy": "node build-and-deploy.js", "test:health": "node test-services.js", "cleanup": "node build-and-deploy.js --cleanup-only"}, "keywords": ["microservices", "e-learning", "english", "course", "platform"], "author": "Time Course Team", "license": "MIT", "devDependencies": {"axios": "^1.12.2"}}